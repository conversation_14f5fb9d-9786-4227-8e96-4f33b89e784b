<?php
///////////////////////////////////////////////////////////
/////////////SESSION NEEDED TO OBTAIN SHOP ID//////////////
///////////////////////////////////////////////////////////

// Get filter parameters
$from = $_GET["from"];
$to = $_GET["to"];
$financial_year = isset($_GET["financial_year"]) ? $_GET["financial_year"] : '';

// Handle branch selection logic (same as sales_all_branches.php)
if(isset($_GET["all_branches"])) {
    $q="select shop_id from branch";
    $q_rs=mysqli_query($connection, $q);
    while($row_user = mysqli_fetch_assoc($q_rs)){
       $branch[] = $row_user["shop_id"];
    }
    $sp_name="- All Branches";
} else {
    if(isset($_GET["plmtt"])) {
        $q="select shop_id from branch where company='P'";
        $q_rs=mysqli_query($connection, $q);
        while($row_user = mysqli_fetch_assoc($q_rs)){
           $branch[] = $row_user["shop_id"];
        }
        $sp_name="- PLMTT Branches";
    } else {
        if(isset($_GET['branch']) && !empty($_GET['branch'])) {
            $branch=$_GET['branch'];
            $sp_name ="- Branch [";
            foreach ($branch as $names) {
                $sp_name .=$names.' ';
            }
            $sp_name .=" ]";
        } else {
            // If no branch selection, show all branches
            $q="select shop_id from branch";
            $q_rs=mysqli_query($connection, $q);
            while($row_user = mysqli_fetch_assoc($q_rs)){
               $branch[] = $row_user["shop_id"];
            }
            $sp_name="- All Branches";
        }
    }
}

// Include invoicedetails database connection
include_once('../../cnxn/cnxn_invoicedetails.php');

?>
<script language="javascript">
function printdiv(printpage)
{
var headstr = "<html><head><title>Duplicate Invoices Report</title></head><body>";
var footstr = "</body>";
var newstr = document.all.item(printpage).innerHTML;
var oldstr = document.body.innerHTML;
document.body.innerHTML = headstr+newstr+footstr;
window.print();
document.body.innerHTML = oldstr;
return false;
}
</script>

<div class="max-width bevelBox" style="padding-bottom:20px;">

<div class="box">

<div id="div_print">

<?php
$filter_title = "";
if(!empty($financial_year)) {
    $filter_title .= " | Financial Year: " . $financial_year;
}
?>

<h3>Duplicate Invoices Report from <?=$from?> to <?=$to?> <?=$sp_name?> <?=$filter_title?></h3>

<table class="table table-striped table-bordered" width="100%">
   <thead class="thead-dark">
       <tr>
           <th>Invoice Number</th>
           <th>Branch Code</th>
           <th>Invoice Date</th>
           <th class="text-right">Sales Amount (Rs.)</th>
           <th class="text-right">Financial Year</th>
       </tr>
   </thead>
   <tbody>

<?php

// Build the SQL query
$sql = "SELECT inv_number, inv_date, inv_sales_amt, branc_code, FinancialYear, IsDeleted
        FROM invoicedetailsfinancialyear_duplicates
        WHERE 1=1";

// Add date filters
if(!empty($from)) {
    $sql .= " AND inv_date >= '" . $from . "'";
}

if(!empty($to)) {
    $sql .= " AND inv_date <= '" . $to . "'";
}

// Add financial year filter
if(!empty($financial_year)) {
    $sql .= " AND FinancialYear = '" . $financial_year . "'";
}

// Add branch filter
if(!empty($branch)) {
    $branch_list = "'" . implode("','", $branch) . "'";
    $sql .= " AND branc_code IN (" . $branch_list . ")";
}

$sql .= " ORDER BY branc_code, inv_date, inv_number";

$result = mysqli_query($invoice_connection, $sql);

// Initialize totals
$total_records = 0;
$total_sales_amt = 0;

if($result && mysqli_num_rows($result) > 0) {
    while($row = mysqli_fetch_assoc($result)) {
        $total_records++;
        $total_sales_amt += ($row["inv_sales_amt"] ?? 0);

        // Format the date to remove time portion
        $formatted_date = date('Y-m-d', strtotime($row["inv_date"]));

        ?>
        <tr>
            <td><?=$row["inv_number"]?></td>
            <td><?=$row["branc_code"]?></td>
            <td><?=$formatted_date?></td>
            <td class="text-right"><?=number_format($row["inv_sales_amt"] ?? 0, 2)?></td>
            <td class="text-right"><?=$row["FinancialYear"]?></td>
        </tr>
        <?php
    }
} else {
    ?>
    <tr>
        <td colspan="5" class="text-center text-muted">No duplicate invoices found for the selected criteria.</td>
    </tr>
    <?php
}
?>
   </tbody>
   <tfoot class="thead-light">
       <tr>
           <th colspan="3" class="text-right">GRAND TOTAL (<?=$total_records?> records)</th>
           <th class="text-right"><?=number_format($total_sales_amt, 2)?></th>
           <th></th>
       </tr>
   </tfoot>

<?php
$g="select now() as time";
$g_rs=mysqli_query($connection, $g);
$g_rw=mysqli_fetch_assoc($g_rs);
$time=$g_rw["time"];
?>
</table>

<div class="mt-3">
    <small class="text-muted">
        Printed By <?=$_SESSION["user_name"]?> on <?=$time?><br>
        End Of Report
    </small>
</div>
</div>

</div>

<form method="post" action="#">
<input name="b_print" type="button" onClick="printdiv('div_print');" value=" Print " class="btn btn-info pull-left">
</form>

</div>

<?php
// Close the invoice database connection
mysqli_close($invoice_connection);
?>
