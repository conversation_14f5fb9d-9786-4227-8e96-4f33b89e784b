<?php
$user_name=$_SESSION["user_name"];
$pq="select * from user_group where user_name='".$user_name."' and (user_group='MIS' or user_group='MIS_ACC')";
$pq_rs=mysqli_query($connection, $pq);
$pq_rw=mysqli_fetch_assoc($pq_rs);

$pq_rows=mysqli_num_rows($pq_rs);

if($pq_rows>0)
{
?>

<script type="text/javascript">
            // When the document is ready
            $(document).ready(function () {

                $('#from').datepicker({
                    format: "yyyy/mm/dd"
                });
                $('#from').datepicker('setDate', 'now');
                 $('#to').datepicker({
                    format: "yyyy/mm/dd"
                });
                $('#to').datepicker('setDate', 'now');
            });
        </script>

<div class="max-width bevelBox" style="padding-bottom:20px;">

<div class="box box-primary">
    <div class="box-header with-border">
        <h3 class="box-title">Cancelled Invoices Report - All Branches</h3>
    </div>
    <div class="box-body">
        <form method="get" action="admin_index.php">
            <input type="hidden" name="p" value="invoice_cancel_all_branches">
            <input type="hidden" name="m" value="general">

            <div class="row">
                <div class="col-md-12">
                    <table class="table table-borderless">
  <tr>
    <td width="15%"><strong>Branch Selection:</strong></td>
    <td width="20%">
      <div class="form-check">
        <input type="checkbox" class="form-check-input" name="all_branches">
        <label class="form-check-label">All Branches</label>
      </div>
      <div class="form-check">
        <input type="checkbox" class="form-check-input" name="plmtt">
        <label class="form-check-label">PLMTT Branches</label>
      </div>
    </td>
    <td width="15%"><strong>Specific Branches:</strong></td>
    <td width="20%">
      <select class="form-control" name="branch[]" multiple>
        <?php
        $resultshopID=mysqli_query($connection,"SELECT shop_id,shop_name FROM branch order by pid ") or die("Error loading branches");
        while ($rowSHid=mysqli_fetch_array($resultshopID)) {
          $name1q=$rowSHid["shop_id"];
          $name1q2=$rowSHid["shop_name"];
        ?>
        <option value="<?=$name1q?>"><?php echo $name1q2; ?></option>
        <?php } ?>
      </select>
    </td>
    <td width="30%">&nbsp;</td>
  </tr>

  <tr>
    <td><strong>From Date:</strong></td>
    <td>
      <input type="text" name="from" id="from" class="form-control" placeholder="YYYY/MM/DD" required>
    </td>
    <td><strong>To Date:</strong></td>
    <td>
      <input type="text" name="to" id="to" class="form-control" placeholder="YYYY/MM/DD" required>
    </td>
    <td>&nbsp;</td>
  </tr>

  <tr>
    <td><strong>Financial Year:</strong></td>
    <td>
      <select name="financial_year" class="form-control">
        <option value="">All Financial Years</option>
        <option value="2020-2021">2020-2021</option>
        <option value="2021-2022">2021-2022</option>
        <option value="2022-2023">2022-2023</option>
        <option value="2023-2024">2023-2024</option>
        <option value="2024-2025">2024-2025</option>
      </select>
    </td>
    <td colspan="3">&nbsp;</td>
  </tr>

                      <tr>
                        <td colspan="5" style="padding-top: 20px;">
                          <input type="submit" name="button" value="Generate Report" class="btn btn-primary">
                          &nbsp;&nbsp;
                          <input type="reset" value="Clear" class="btn btn-secondary">
                        </td>
                      </tr>
                    </table>
                </div>
            </div>
        </form>
    </div>
</div>

</div>

<?php
}
else {
    echo "<div class='alert alert-danger'>You don't have permission to access this report.</div>";
}
?>
