<?php
///////////////////////////////////////////////////////////
/////////////SESSION NEEDED TO OBTAIN SHOP ID//////////////
///////////////////////////////////////////////////////////

// Get filter parameters
$from = $_GET["from"];
$to = $_GET["to"];
$financial_year = isset($_GET["financial_year"]) ? $_GET["financial_year"] : '';

// Create connection to invoicedetails database
$invoice_connection = mysqli_connect('**************:3306','root','data@123','invoicedetails');

// If connection was not successful, handle the error
if($invoice_connection === false) {
  echo "Error: Could not connect to invoicedetails database";
  exit;
}

?>
<script language="javascript">
function printdiv(printpage)
{
var headstr = "<html><head><title>Cancelled Invoices Report</title></head><body>";
var footstr = "</body>";
var newstr = document.all.item(printpage).innerHTML;
var oldstr = document.body.innerHTML;
document.body.innerHTML = headstr+newstr+footstr;
window.print();
document.body.innerHTML = oldstr;
return false;
}
</script>

<div class="max-width bevelBox" style="padding-bottom:20px;">

<div class="box">

<div id="div_print">

<?php
$filter_title = "";
if(!empty($financial_year)) {
    $filter_title .= " | Financial Year: " . $financial_year;
}
?>

<h3>Cancelled Invoices Report from <?=$from?> to <?=$to?> <?=$filter_title?></h3>

<table width="100%">
  <tbody>
   <thead>
       <tr>
           <td><strong>Branch Code</strong></td>
           <td><strong>Invoice Number</strong></td>
           <td><strong>Invoice Date</strong></td>
           <td align="right"><strong>Sales Amount (Rs.)</strong></td>
           <td align="right"><strong>Cash Amount (Rs.)</strong></td>
           <td align="right"><strong>Credit Amount (Rs.)</strong></td>
           <td><strong>Financial Year</strong></td>
       </tr>
   </thead>

<?php

// Build the SQL query
$sql = "SELECT inv_number, inv_date, inv_sales_amt, branc_code, cashamt, creditamt, FinancialYear, IsDeleted
        FROM invoicedetailsfinancialyear_cancel
        WHERE 1=1";

// Add date filters
if(!empty($from)) {
    $sql .= " AND inv_date >= '" . $from . "'";
}

if(!empty($to)) {
    $sql .= " AND inv_date <= '" . $to . "'";
}

// Add financial year filter
if(!empty($financial_year)) {
    $sql .= " AND FinancialYear = '" . $financial_year . "'";
}

$sql .= " ORDER BY branc_code, inv_date, inv_number";

$result = mysqli_query($invoice_connection, $sql);

// Initialize totals
$total_records = 0;
$total_sales_amt = 0;
$total_cash_amt = 0;
$total_credit_amt = 0;

if($result && mysqli_num_rows($result) > 0) {
    while($row = mysqli_fetch_assoc($result)) {
        $total_records++;
        $total_sales_amt += $row["inv_sales_amt"];
        $total_cash_amt += $row["cashamt"];
        $total_credit_amt += $row["creditamt"];

        ?>
        <tr>
            <td><?=$row["branc_code"]?></td>
            <td><?=$row["inv_number"]?></td>
            <td><?=$row["inv_date"]?></td>
            <td align="right"><?=number_format($row["inv_sales_amt"], 2)?></td>
            <td align="right"><?=number_format($row["cashamt"], 2)?></td>
            <td align="right"><?=number_format($row["creditamt"], 2)?></td>
            <td><?=$row["FinancialYear"]?></td>
        </tr>
        <?php
    }
} else {
    ?>
    <tr>
        <td colspan="8" align="center">No cancelled invoices found for the selected criteria.</td>
    </tr>
    <?php
}
?>

<tr>
    <td colspan="8"><hr style="width: 100%; color: black; height: 1px; background-color:black;" /></td>
</tr>

<tr>
    <td align="right"><strong>GRAND TOTAL (<?=$total_records?> records)</strong></td>
    <td></td>
    <td></td>
    <td align="right"><strong><?=number_format($total_sales_amt, 2)?></strong></td>
    <td align="right"><strong><?=number_format($total_cash_amt, 2)?></strong></td>
    <td align="right"><strong><?=number_format($total_credit_amt, 2)?></strong></td>
    <td></td>
    <td></td>
</tr>

<tr><td colspan="8">&nbsp;</td></tr>

<?php
$g="select now() as time";
$g_rs=mysqli_query($connection, $g);
$g_rw=mysqli_fetch_assoc($g_rs);
$time=$g_rw["time"];
?>
<tr><td colspan="8">Printed By <?=$_SESSION["user_name"]?> on <?=$time?></td></tr>
<tr><td colspan="8"><hr style="width: 100%; color: black; height: 1px; background-color:black;" /></td></tr>
<tr><td colspan="8">End Of Report</td></tr>

</tbody>
</table>
</div>

</div>

<form method="post" action="#">
<input name="b_print" type="button" onClick="printdiv('div_print');" value=" Print " class="btn btn-info pull-left">
</form>

</div>

<?php
// Close the invoice database connection
mysqli_close($invoice_connection);
?>
